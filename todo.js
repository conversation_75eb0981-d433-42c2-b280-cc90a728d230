document.addEventListener('DOMContentLoaded', () => {
    // URL Blocklist feature
    // Detect if user is on Mac or Windows/other
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

    // Update shortcut hints based on OS
    const shortcutHints = document.querySelectorAll('.shortcut-hint');
    shortcutHints.forEach(hint => {
        const text = hint.innerHTML;
        if (text.includes('Ctrl + Enter')) {
            const action = text.includes('to save') ? 'to save' : 'to add';
            hint.innerHTML = `<i class="fas fa-keyboard"></i> Press ${isMac ? 'Command' : 'Ctrl'} + Enter ${action}`;
        }
    });

    // DOM Elements
    const taskTitleInput = document.getElementById('task-title');
    const taskDescInput = document.getElementById('task-description');
    const addTaskBtn = document.getElementById('add-task');
    const taskList = document.getElementById('task-list');
    const archiveList = document.getElementById('archive-list');
    const toggleArchiveBtn = document.getElementById('toggle-archive');
    const editModal = document.getElementById('edit-modal');
    const closeModal = document.querySelectorAll('.close-modal');
    const editTitleInput = document.getElementById('edit-title');
    const editDescInput = document.getElementById('edit-description');
    const saveEditBtn = document.getElementById('save-edit');
    const calendarEl = document.getElementById('calendar');

    // Task management
    let tasks = [];
    let archivedTasks = [];
    let currentEditId = null;
    let blockedUrls = [];
    let isBlockingEnabled = true;
    let blockingStatus = { blockedAttempts: 0 };
    let isArchiveExpanded = false;
    let calendar = null;
    let notificationManager = null;
    let googleAuthService = null;
    let googleCalendarService = null;

    // Load tasks and settings from Chrome storage
    const loadTasks = () => {
        chrome.storage.local.get(['tasks', 'archivedTasks', 'archiveState', 'blockedUrls', 'isBlockingEnabled', 'blockingStatus'], (result) => {
            if (result.tasks) {
                tasks = result.tasks;
            }

            if (result.archivedTasks) {
                archivedTasks = result.archivedTasks;
            }

            if (result.archiveState !== undefined) {
                isArchiveExpanded = result.archiveState;
                updateArchiveToggleState();
            }

            if (result.blockedUrls) {
                blockedUrls = result.blockedUrls;
                renderBlocklist();
            }

            if (result.isBlockingEnabled !== undefined) {
                isBlockingEnabled = result.isBlockingEnabled;
                document.getElementById('blocking-enabled').checked = isBlockingEnabled;
            }

            if (result.blockingStatus) {
                blockingStatus = result.blockingStatus;
                updateBlockedAttemptsCount();
            }

            renderTasks();
            renderArchivedTasks();
            updateCalendarEvents();
        });
    };

    // Save tasks to Chrome storage
    const saveTasks = () => {
        chrome.storage.local.set({
            tasks,
            archivedTasks,
            archiveState: isArchiveExpanded,
            blockedUrls,
            isBlockingEnabled,
            blockingStatus
        });
    };

    // Generate unique ID for tasks
    const generateId = () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    };

    // Render tasks to DOM
    const renderTasks = () => {
        taskList.innerHTML = '';

        if (tasks.length === 0) {
            taskList.innerHTML = '<p class="empty-list">No tasks yet. Add one above!</p>';
            return;
        }

        tasks.forEach(task => {
            const taskItem = document.createElement('li');
            taskItem.classList.add('task-item');
            taskItem.setAttribute('data-id', task.id);
            // Make task draggable for both reordering and calendar scheduling
            taskItem.setAttribute('draggable', 'true');
            taskItem.classList.add('draggable-task');

            // Add completed class if task is completed
            if (task.completed) {
                taskItem.classList.add('completed');
            }

            taskItem.innerHTML = `
                <div class="task-content">
                    <label class="task-checkbox">
                        <input type="checkbox" class="task-check" ${task.completed ? 'checked' : ''}>
                        <span class="checkmark"></span>
                    </label>
                    <div class="task-text">
                        <div class="task-title">${task.title}</div>
                        ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                        ${task.scheduled ? `<div class="task-schedule"><i class="fas fa-calendar-alt"></i> ${formatScheduledTime(task)}</div>` : ''}
                    </div>
                </div>
                <div class="task-actions">
                    <button class="edit-btn" title="Edit task"><i class="fas fa-edit"></i></button>
                    <button class="delete-btn" title="Delete task"><i class="fas fa-trash"></i></button>
                </div>
            `;

            taskList.appendChild(taskItem);

            // Add event listeners for edit, delete, and checkbox
            const editBtn = taskItem.querySelector('.edit-btn');
            const deleteBtn = taskItem.querySelector('.delete-btn');
            const checkbox = taskItem.querySelector('.task-check');

            editBtn.addEventListener('click', () => {
                openEditModal(task.id);
            });

            deleteBtn.addEventListener('click', () => {
                deleteTask(task.id);
            });

            checkbox.addEventListener('change', () => {
                toggleTaskCompletion(task.id);
            });

            // Add drag and drop event listeners for task reordering
            taskItem.addEventListener('dragstart', handleDragStart);
            taskItem.addEventListener('dragend', handleDragEnd);

            // Make task items accept drops from calendar events
            taskItem.addEventListener('dragover', (e) => {
                const types = e.dataTransfer.types;
                if (types.includes('application/json')) {
                    // Allow drops from both tasks (for reordering) and calendar events
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                    if (!draggedItem) {
                        // This is likely a calendar event being dragged
                        taskItem.classList.add('calendar-drop-target');
                    } else {
                        // Handle normal task reordering
                        handleDragOver.call(taskItem, e);
                    }
                }
            });

            taskItem.addEventListener('dragenter', (e) => {
                if (!draggedItem) {
                    // This is likely a calendar event being dragged
                    taskItem.classList.add('calendar-drop-target');
                } else {
                    // Handle normal task reordering
                    handleDragEnter.call(taskItem, e);
                }
            });

            taskItem.addEventListener('dragleave', (e) => {
                taskItem.classList.remove('calendar-drop-target');
                if (draggedItem) {
                    // Handle normal task reordering
                    handleDragLeave.call(taskItem, e);
                }
            });

            taskItem.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation(); // Prevent event bubbling

                // Remove highlight from this item
                taskItem.classList.remove('calendar-drop-target');

                // Clear all drop indicators in the UI
                document.querySelectorAll('.drag-target, .calendar-drop-target, .drag-over').forEach(el => {
                    el.classList.remove('drag-target');
                    el.classList.remove('calendar-drop-target');
                    el.classList.remove('drag-over');
                });

                try {
                    // Check if this is a calendar event drop
                    const jsonData = e.dataTransfer.getData('application/json');
                    if (jsonData) {
                        const data = JSON.parse(jsonData);
                        if (data.type === 'calendar-event') {
                            console.log('Unscheduling task from item drop:', data.taskId);
                            // Unschedule the task
                            unscheduleTask(data.taskId);
                            return;
                        }
                    }

                    // If not a calendar event, handle normal task reordering
                    if (draggedItem) {
                        handleDrop.call(taskItem, e);
                    }
                } catch (error) {
                    console.error('Error processing drop on task item:', error);
                }
            });

            // Set data for calendar drag and drop
            taskItem.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('application/json', JSON.stringify({
                    taskId: task.id,
                    type: 'task'
                }));
            });
        });
    };

    // Add a new task
    const addTask = () => {
        const title = taskTitleInput.value.trim();
        const description = taskDescInput.value.trim();

        if (!title) return;

        const newTask = {
            id: generateId(),
            title,
            description,
            completed: false,
            createdAt: new Date().toISOString(),
            scheduled: false,
            scheduledStart: null,
            scheduledEnd: null
        };

        tasks.push(newTask);
        saveTasks();
        renderTasks();
        // Note: No longer automatically adding to calendar

        // Clear inputs
        taskTitleInput.value = '';
        taskDescInput.value = '';
        taskTitleInput.focus();
    };

    // Delete a task
    const deleteTask = (id) => {
        tasks = tasks.filter(task => task.id !== id);
        saveTasks();
        renderTasks();
        updateCalendarEvents(); // Update calendar after deletion
    };

    // Open edit modal
    const openEditModal = (id) => {
        const task = tasks.find(task => task.id === id);
        if (!task) return;

        currentEditId = id;
        editTitleInput.value = task.title;
        editDescInput.value = task.description || '';

        editModal.style.display = 'block';

        // Set focus to the task name field
        setTimeout(() => {
            editTitleInput.focus();
            // Optionally position cursor at the end of the text
            editTitleInput.selectionStart = editTitleInput.selectionEnd = editTitleInput.value.length;
        }, 10); // Small delay to ensure the modal is fully visible
    };

    // Close edit modal
    const closeEditModal = () => {
        editModal.style.display = 'none';
        currentEditId = null;
    };

    // Settings Modal Elements
    const settingsBtn = document.getElementById('settings-btn');
    const settingsModal = document.getElementById('settings-modal');
    const blocklistUrl = document.getElementById('blocklist-url');
    const addUrlBtn = document.getElementById('add-url-btn');
    const blocklistEl = document.getElementById('blocklist');
    const urlValidation = document.getElementById('url-validation');
    const blockingEnabledToggle = document.getElementById('blocking-enabled');
    const blockedAttemptsEl = document.getElementById('blocked-attempts');

    // Notification settings elements
    const notificationsEnabledToggle = document.getElementById('notifications-enabled');
    const notificationPermissionStatus = document.getElementById('notification-permission-status');
    const requestNotificationPermissionBtn = document.getElementById('request-notification-permission');

    // Open settings modal
    const openSettingsModal = () => {
        settingsModal.style.display = 'block';

        // Ensure tabs are properly initialized
        const activeTabBtn = document.querySelector('.settings-tabs .tab-btn.active');
        if (activeTabBtn) {
            const tabId = activeTabBtn.getAttribute('data-tab');

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Show active tab content
            const activeContent = document.getElementById(`${tabId}-tab`);
            if (activeContent) {
                activeContent.classList.add('active');
            }
        }
    };

    // Close settings modal
    const closeSettingsModal = () => {
        settingsModal.style.display = 'none';
        // Reset validation message
        urlValidation.textContent = '';
        urlValidation.className = 'validation-message';
    };

    // Handle settings tab switching
    const setupSettingsTabs = () => {
        const tabButtons = document.querySelectorAll('.settings-tabs .tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    };

    // Validate URL format
    const isValidUrl = (url) => {
        // Basic URL validation - accept domain names without protocol
        // This regex allows domains like 'example.com', 'sub.example.com', etc.
        const urlRegex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w.-]*)*\/?$/;
        return urlRegex.test(url);
    };

    // Format URL (ensure it has a protocol)
    const formatUrl = (url) => {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return 'https://' + url;
        }
        return url;
    };

    // Extract domain from URL
    const extractDomain = (url) => {
        try {
            // Remove protocol and get domain
            let domain = url.replace(/^https?:\/\//, '');
            // Remove path if any
            domain = domain.split('/')[0];
            return domain.toLowerCase();
        } catch (e) {
            return url;
        }
    };

    // Add URL to blocklist
    const addUrlToBlocklist = () => {
        const url = blocklistUrl.value.trim();

        // Validate URL
        if (!url) {
            urlValidation.textContent = 'Please enter a URL';
            return;
        }

        if (!isValidUrl(url)) {
            urlValidation.textContent = 'Please enter a valid URL (e.g., example.com)';
            return;
        }

        // Check if URL already exists in blocklist
        const domain = extractDomain(url);
        if (blockedUrls.some(item => extractDomain(item) === domain)) {
            urlValidation.textContent = 'This URL is already in your blocklist';
            return;
        }

        // Add URL to blocklist
        blockedUrls.push(formatUrl(url));
        saveTasks(); // Save to Chrome storage
        renderBlocklist();

        // Clear input and validation message
        blocklistUrl.value = '';
        urlValidation.textContent = '';
    };

    // Delete URL from blocklist
    const deleteUrlFromBlocklist = (index) => {
        blockedUrls.splice(index, 1);
        saveTasks(); // Save to Chrome storage
        renderBlocklist();
    };

    // Update blocked attempts count
    const updateBlockedAttemptsCount = () => {
        if (blockedAttemptsEl) {
            blockedAttemptsEl.textContent = blockingStatus.blockedAttempts || 0;
        }
    };

    // Toggle website blocking
    const toggleWebsiteBlocking = (enabled) => {
        isBlockingEnabled = enabled;
        saveTasks();
    };

    // Render blocklist
    const renderBlocklist = () => {
        blocklistEl.innerHTML = '';

        if (blockedUrls.length === 0) {
            blocklistEl.innerHTML = '<li class="empty-list">No blocked URLs yet</li>';
            return;
        }

        blockedUrls.forEach((url, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <span>${url}</span>
                <button class="delete-url-btn" data-index="${index}" title="Delete URL">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            blocklistEl.appendChild(li);
        });

        // Add event listeners to delete buttons
        document.querySelectorAll('.delete-url-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                deleteUrlFromBlocklist(index);
            });
        });
    };

    // Save edited task
    const saveEditedTask = () => {
        if (!currentEditId) return;

        const title = editTitleInput.value.trim();
        if (!title) return;

        const description = editDescInput.value.trim();

        const taskIndex = tasks.findIndex(task => task.id === currentEditId);
        if (taskIndex === -1) return;

        tasks[taskIndex].title = title;
        tasks[taskIndex].description = description;
        // Preserve the completed state

        saveTasks();
        renderTasks();
        updateCalendarEvents(); // Update calendar after editing task
        closeEditModal();
    };

    // Toggle task completion status and move to archive if completed
    const toggleTaskCompletion = (id) => {
        const taskIndex = tasks.findIndex(task => task.id === id);
        if (taskIndex === -1) return;

        // Toggle the completed status
        tasks[taskIndex].completed = !tasks[taskIndex].completed;

        // If task is now completed, move it to archive
        if (tasks[taskIndex].completed) {
            const completedTask = tasks[taskIndex];
            completedTask.archivedAt = new Date().toISOString();
            archivedTasks.push(completedTask);
            tasks.splice(taskIndex, 1);
        }

        saveTasks();
        renderTasks();
        renderArchivedTasks();
        updateCalendarEvents(); // Update calendar after task status change
    };

    // Toggle archive visibility
    const toggleArchive = () => {
        isArchiveExpanded = !isArchiveExpanded;
        updateArchiveToggleState();
        saveTasks();
    };

    // Update archive toggle button state
    const updateArchiveToggleState = () => {
        const archiveList = document.getElementById('archive-list');

        if (isArchiveExpanded) {
            archiveList.classList.add('expanded');
            toggleArchiveBtn.classList.add('active');
        } else {
            archiveList.classList.remove('expanded');
            toggleArchiveBtn.classList.remove('active');
        }
    };

    // Render archived tasks
    const renderArchivedTasks = () => {
        archiveList.innerHTML = '';

        if (archivedTasks.length === 0) {
            archiveList.innerHTML = '<p class="empty-list">No completed tasks yet.</p>';
            return;
        }

        // Sort archived tasks by archive date (newest first)
        archivedTasks.sort((a, b) => new Date(b.archivedAt) - new Date(a.archivedAt));

        archivedTasks.forEach(task => {
            const taskItem = document.createElement('li');
            taskItem.classList.add('task-item', 'archive-item');
            taskItem.setAttribute('data-id', task.id);

            taskItem.innerHTML = `
                <div class="task-content">
                    <label class="task-checkbox">
                        <input type="checkbox" class="task-check" checked>
                        <span class="checkmark"></span>
                    </label>
                    <div class="task-text">
                        <div class="task-title">${task.title}</div>
                        ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                        <div class="archive-date">Completed: ${new Date(task.archivedAt).toLocaleDateString()}</div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="delete-archive-btn" title="Delete from archive"><i class="fas fa-trash"></i></button>
                </div>
            `;

            archiveList.appendChild(taskItem);

            // Add event listeners for delete and unarchive
            const deleteBtn = taskItem.querySelector('.delete-archive-btn');
            const checkbox = taskItem.querySelector('.task-check');

            deleteBtn.addEventListener('click', () => {
                deleteFromArchive(task.id);
            });

            checkbox.addEventListener('change', () => {
                if (!checkbox.checked) {
                    unarchiveTask(task.id);
                }
            });
        });
    };

    // Delete task from archive
    const deleteFromArchive = (id) => {
        archivedTasks = archivedTasks.filter(task => task.id !== id);
        saveTasks();
        renderArchivedTasks();
        updateCalendarEvents(); // Update calendar after deletion
    };

    // Move task from archive back to active tasks
    const unarchiveTask = (id) => {
        const taskIndex = archivedTasks.findIndex(task => task.id === id);
        if (taskIndex === -1) return;

        // Get the task from archive
        const taskToUnarchive = archivedTasks[taskIndex];

        // Reset completed status and remove archive date
        taskToUnarchive.completed = false;
        delete taskToUnarchive.archivedAt;

        // Add to active tasks and remove from archive
        tasks.push(taskToUnarchive);
        archivedTasks.splice(taskIndex, 1);

        saveTasks();
        renderTasks();
        renderArchivedTasks();
        updateCalendarEvents(); // Update calendar after unarchiving
    };

    // Drag and drop functionality
    let draggedItem = null;

    function handleDragStart(e) {
        draggedItem = this;
        this.classList.add('dragging');

        // Required for Firefox
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.innerHTML);
    }

    function handleDragEnd() {
        this.classList.remove('dragging');
        draggedItem = null;
    }

    // Schedule a task to a specific time slot or as a full-day event
    const scheduleTask = (taskId, dateTimeISOString, isFullDay = false) => {
        console.log(`Scheduling task ${taskId} at ${dateTimeISOString}, isFullDay: ${isFullDay}`);
        const taskIndex = tasks.findIndex(task => task.id === taskId);
        const archivedTaskIndex = archivedTasks.findIndex(task => task.id === taskId);

        let task;
        let isArchived = false;

        if (taskIndex !== -1) {
            task = tasks[taskIndex];
        } else if (archivedTaskIndex !== -1) {
            task = archivedTasks[archivedTaskIndex];
            isArchived = true;
        } else {
            return; // Task not found
        }

        // Parse the target datetime for the new start time
        const scheduledStart = new Date(dateTimeISOString);

        // Handle end time based on whether it's a full-day or time-specific event
        let scheduledEnd;

        if (isFullDay) {
            // For full-day events, set end time to 23:59:59 of the same day
            scheduledEnd = new Date(scheduledStart);
            scheduledEnd.setHours(23, 59, 59, 999);
        } else {
            // For time-based events, calculate end time based on duration
            if (task.scheduled && task.scheduledStart && task.scheduledEnd) {
                // Calculate current duration in minutes
                const currentStart = new Date(task.scheduledStart);
                const currentEnd = new Date(task.scheduledEnd);
                const durationMs = currentEnd - currentStart;
                const durationMinutes = Math.round(durationMs / 60000);

                // Apply the same duration to the new scheduled time
                scheduledEnd = new Date(scheduledStart);
                scheduledEnd.setMinutes(scheduledEnd.getMinutes() + durationMinutes);
            } else {
                // Default to 30 minutes if no previous duration exists
                scheduledEnd = new Date(scheduledStart);
                scheduledEnd.setMinutes(scheduledEnd.getMinutes() + 30);
            }
        }

        // Update the task
        const updateData = {
            scheduled: true,
            scheduledStart: scheduledStart.toISOString(),
            scheduledEnd: scheduledEnd ? scheduledEnd.toISOString() : null,
            isFullDay: isFullDay
        };

        if (isArchived) {
            Object.assign(archivedTasks[archivedTaskIndex], updateData);
        } else {
            Object.assign(tasks[taskIndex], updateData);
        }

        // Save and update UI
        saveTasks();
        renderTasks();
        if (isArchiveExpanded) {
            renderArchivedTasks();
        }

        // Force a complete calendar refresh
        updateCalendarEvents();

        // Re-render the calendar to ensure it reflects the changes immediately
        if (calendar) {
            calendar.render();
        }

        // Show a notification that websites will be blocked during this time if blocking is enabled
        if (isBlockingEnabled && blockedUrls.length > 0) {
            const taskTitle = isArchived ? archivedTasks[archivedTaskIndex].title : tasks[taskIndex].title;
            const startTime = formatScheduledTime({ scheduledStart: scheduledStart.toISOString() });

            // Format the notification message based on whether it's a full-day event or time-specific
            let timeDisplay;
            let blockingMessage = '';
            if (isFullDay) {
                timeDisplay = 'All day';
                const date = new Date(scheduledStart);
                const formattedDate = date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                });
                blockingMessage = `Websites in your blocklist will be blocked all day on ${formattedDate} for task "${taskTitle}"`;
            } else if (scheduledEnd) {
                const endTime = formatScheduledTime({ scheduledEnd: scheduledEnd.toISOString() });
                timeDisplay = `${startTime} - ${endTime}`;
                blockingMessage = `Websites in your blocklist will be blocked during "${taskTitle}" (${timeDisplay})`;
            } else {
                timeDisplay = startTime;
                blockingMessage = `Websites in your blocklist will be blocked during "${taskTitle}" (${timeDisplay})`;
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-shield-alt"></i>
                    <span>${blockingMessage}</span>
                </div>
                <button class="close-notification"><i class="fas fa-times"></i></button>
            `;

            document.body.appendChild(notification);

            // Show notification with animation
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Add event listener to close button
            notification.querySelector('.close-notification').addEventListener('click', () => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            });

            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.classList.remove('show');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        return true;
    };

    // Unschedule a task (remove its calendar assignment)
    const unscheduleTask = (taskId) => {
        const taskIndex = tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return;

        // Remove scheduling information
        tasks[taskIndex].scheduled = false;
        tasks[taskIndex].scheduledStart = null;
        tasks[taskIndex].scheduledEnd = null;

        // Save and update UI
        saveTasks();
        renderTasks();
        updateCalendarEvents();
    };
    function handleDragOver(e) {
        e.preventDefault();
        return false;
    }

    function handleDragEnter(e) {
        e.preventDefault();
        this.classList.add('drag-over');
    }

    function handleDragLeave() {
        this.classList.remove('drag-over');
    }

    function handleDrop(e) {
        e.stopPropagation();

        if (draggedItem !== this) {
            // Get the dragged and dropped task IDs
            const draggedId = draggedItem.getAttribute('data-id');
            const droppedId = this.getAttribute('data-id');

            // Find their indices in the tasks array
            const draggedIndex = tasks.findIndex(task => task.id === draggedId);
            const droppedIndex = tasks.findIndex(task => task.id === droppedId);

            // Reorder the tasks array
            const [removedTask] = tasks.splice(draggedIndex, 1);
            tasks.splice(droppedIndex, 0, removedTask);

            // Save and re-render
            saveTasks();
            renderTasks();
            updateCalendarEvents(); // Update calendar after reordering
        }

        return false;
    }

    // Event Listeners
    addTaskBtn.addEventListener('click', addTask);

    // Settings button click
    settingsBtn.addEventListener('click', openSettingsModal);

    // Close settings modal
    closeModal.forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.closest('#edit-modal')) {
                closeEditModal();
            } else if (this.closest('#settings-modal')) {
                closeSettingsModal();
            }
        });
    });

    // Initialize settings tabs
    setupSettingsTabs();

    // Add URL button click
    addUrlBtn.addEventListener('click', addUrlToBlocklist);

    // Add URL on Enter key
    blocklistUrl.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addUrlToBlocklist();
        }
    });

    // Toggle blocking enabled
    blockingEnabledToggle.addEventListener('change', (e) => {
        toggleWebsiteBlocking(e.target.checked);
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === editModal) {
            closeEditModal();
        } else if (e.target === settingsModal) {
            closeSettingsModal();
        }
    });

    // Handle keyboard shortcuts for adding tasks
    const handleKeyboardShortcut = (e) => {
        // Check for Cmd+Enter (Mac) or Ctrl+Enter (Windows/Linux)
        if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
            e.preventDefault();
            addTask();
        }
    };

    // Regular Enter key in the title field
    taskTitleInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.metaKey && !e.ctrlKey) {
            addTask();
        }
    });

    // Add keyboard shortcut listeners to both input fields
    taskTitleInput.addEventListener('keydown', handleKeyboardShortcut);
    taskDescInput.addEventListener('keydown', handleKeyboardShortcut);

    // This is now handled by the forEach loop above

    saveEditBtn.addEventListener('click', saveEditedTask);

    toggleArchiveBtn.addEventListener('click', toggleArchive);

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === editModal) {
            closeEditModal();
        }
    });

    // Keyboard shortcuts for the edit modal
    window.addEventListener('keydown', (e) => {
        // Only process if the edit modal is open
        if (editModal.style.display === 'block') {
            // Close with Escape key
            if (e.key === 'Escape') {
                closeEditModal();
            }
            // Save with Enter key (not in textarea)
            else if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                saveEditedTask();
            }
        }
    });

    // Add Enter key handler for the edit title input
    editTitleInput.addEventListener('keydown', (e) => {
        // Use Ctrl+Enter (Windows) or Command+Enter (Mac) to save
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            saveEditedTask();
        }
    });

    // Add the same shortcut to the description input
    editDescInput.addEventListener('keydown', (e) => {
        // Use Ctrl+Enter (Windows) or Command+Enter (Mac) to save
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            saveEditedTask();
        }
    });

    // Update task schedule when event is resized or moved
    const handleEventUpdate = (updatedEvent) => {
        try {
            // Find the task in both active and archived tasks
            let task = tasks.find(t => t.id === updatedEvent.id) ||
                     archivedTasks.find(t => t.id === updatedEvent.id);

            if (!task) {
                console.error('Task not found for event update:', updatedEvent.id);
                return;
            }

            // Validate and format the date
            const startDate = new Date(updatedEvent.date);
            if (isNaN(startDate.getTime())) {
                console.error('Invalid start date:', updatedEvent.date);
                return;
            }

            // Check if this is a full-day event (no end time or explicitly marked as full day)
            const isFullDay = !updatedEvent.endTime || updatedEvent.isFullDay;

            // Format dates consistently
            const formattedStart = startDate.toISOString();
            let formattedEnd = null;

            if (!isFullDay && updatedEvent.endTime) {
                const endDate = new Date(updatedEvent.endTime);
                if (!isNaN(endDate.getTime())) {
                    formattedEnd = endDate.toISOString();
                } else {
                    console.warn('Invalid end date, falling back to full day event');
                }
            }

            // Update the task properties
            task.scheduledStart = formattedStart;
            task.scheduledEnd = formattedEnd;
            task.isFullDay = isFullDay;
            task.scheduled = true;

            console.log('Updated task schedule:', {
                id: task.id,
                scheduledStart: task.scheduledStart,
                scheduledEnd: task.scheduledEnd,
                isFullDay: task.isFullDay
            });

            // Save the updated tasks
            saveTasks();

            // Update the UI
            updateCalendarEvents();
            renderTasks();

            // Refresh archived tasks if they're being shown
            if (typeof showArchived !== 'undefined' && showArchived) {
                renderArchivedTasks();
            }

            // Force calendar to re-render if available
            if (calendar && typeof calendar.render === 'function') {
                calendar.render();
            }
        } catch (error) {
            console.error('Error in handleEventUpdate:', error);
        }
    };

    // Initialize notification manager
    const initializeNotifications = () => {
        // Check if the notification manager class is available
        if (window.TaskNotificationManager) {
            notificationManager = new TaskNotificationManager();

            // Request permission if not already granted
            notificationManager.checkPermission().then(hasPermission => {
                if (hasPermission) {
                    console.log('Notification permission granted, scheduling notifications');
                    // Schedule notifications for all existing tasks
                    notificationManager.scheduleAllTaskNotifications(tasks, archivedTasks);
                } else {
                    console.log('Notification permission not granted');
                }

                // Update UI to reflect permission status
                updateNotificationPermissionUI();
            });

            // Set up notification settings UI
            setupNotificationSettings();
        } else {
            console.warn('TaskNotificationManager not available. Make sure notification-utils.js is loaded');
        }
    };

    // Update notification permission UI
    const updateNotificationPermissionUI = () => {
        if (!('Notification' in window)) {
            notificationPermissionStatus.textContent = 'Not supported in this browser';
            notificationsEnabledToggle.disabled = true;
            requestNotificationPermissionBtn.classList.add('hidden');
            return;
        }

        const permissionStatus = Notification.permission;

        switch (permissionStatus) {
            case 'granted':
                notificationPermissionStatus.textContent = 'Granted';
                notificationPermissionStatus.className = 'permission-granted';
                requestNotificationPermissionBtn.classList.add('hidden');
                notificationsEnabledToggle.disabled = false;
                break;
            case 'denied':
                notificationPermissionStatus.textContent = 'Blocked by browser';
                notificationPermissionStatus.className = 'permission-denied';
                requestNotificationPermissionBtn.classList.add('hidden');
                notificationsEnabledToggle.disabled = true;
                notificationsEnabledToggle.checked = false;
                break;
            default: // 'default' permission state
                notificationPermissionStatus.textContent = 'Not granted';
                notificationPermissionStatus.className = 'permission-default';
                requestNotificationPermissionBtn.classList.remove('hidden');
                break;
        }
    };

    // Set up notification settings
    const setupNotificationSettings = () => {
        // Handle notification toggle
        notificationsEnabledToggle.addEventListener('change', (e) => {
            const isEnabled = e.target.checked;

            if (isEnabled && notificationManager) {
                // Check permission first
                notificationManager.checkPermission().then(hasPermission => {
                    if (hasPermission) {
                        // Schedule notifications for all tasks
                        notificationManager.scheduleAllTaskNotifications(tasks, archivedTasks);
                    } else {
                        // If permission not granted, show request button and uncheck toggle
                        updateNotificationPermissionUI();
                        e.target.checked = false;
                    }
                });
            } else if (notificationManager) {
                // Clear all scheduled notifications
                notificationManager.clearAllNotifications();
            }
        });

        // Handle permission request button
        requestNotificationPermissionBtn.addEventListener('click', async () => {
            if (notificationManager) {
                const granted = await notificationManager.requestPermission();
                updateNotificationPermissionUI();

                if (granted) {
                    // Enable notifications and schedule them
                    notificationsEnabledToggle.checked = true;
                    notificationManager.scheduleAllTaskNotifications(tasks, archivedTasks);
                }
            }
        });
    };

    // Initialize Google Calendar integration
    const initializeGoogleCalendar = async () => {
        // Initialize Google Auth Service
        if (window.GoogleAuthService) {
            console.log('Initializing Google Auth Service...');
            googleAuthService = new GoogleAuthService();

            // Wait for authentication state to be loaded
            await googleAuthService.waitForInitialization();
            console.log('Google Auth Service initialization complete');

            // Initialize Google Calendar Service
            if (window.GoogleCalendarService) {
                googleCalendarService = new GoogleCalendarService(googleAuthService);

                // Set up event listeners for Google Calendar updates
                window.addEventListener('googleCalendarEventsUpdated', (event) => {
                    console.log('Google Calendar events updated:', event.detail);
                    updateCalendarWithGoogleEvents();
                });

                // Set up Google Calendar UI
                setupGoogleCalendarUI();

                // Update UI with current authentication state
                await updateGoogleAuthUI();
                updateGoogleCalendarUI();

                // If authentication was restored and calendar was previously enabled, restore it
                await restoreGoogleCalendarState();

                console.log('Google Calendar integration initialized');
            } else {
                console.warn('GoogleCalendarService not available');
            }
        } else {
            console.warn('GoogleAuthService not available');
        }
    };

    // Initialize Calendar
    const initializeCalendar = () => {
        calendar = new SimpleCalendar(calendarEl, {
            onEventClick: (event) => {
                // Find and open the task when an event is clicked
                const task = findTaskById(event.id);
                if (task) {
                    openEditModal(task.id);
                }
            },
            onEventUpdate: handleEventUpdate,
            onTaskDrop: (dropData) => {
                // Handle task dropped from task list to calendar
                scheduleTask(dropData.taskId, dropData.date, dropData.isFullDay);
            },
            onEventRemove: (eventId) => {
                // Handle event removal (delete button on event)
                const task = findTaskById(eventId);
                if (task) {
                    task.scheduled = false;
                    task.scheduledStart = null;
                    task.scheduledEnd = null;
                    task.isFullDay = false;
                    saveTasks();
                    renderTasks();
                    if (isArchiveExpanded) {
                        renderArchivedTasks();
                    }
                    // Refresh the calendar to show the updated state
                    if (calendar) {
                        updateCalendarEvents();
                        calendar.render();
                    }
                }
            },
            onGoogleEventClick: (event) => {
                // Handle Google Calendar event clicks (show info)
                showGoogleEventInfo(event);
            }
        });

        // Initial render of calendar events
        updateCalendarEvents();
    };

    // Format date for display
    const formatDate = (date) => {
        return date.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    // Format scheduled time for display
    const formatScheduledTime = (task) => {
        if (!task.scheduled || !task.scheduledStart) {
            return '';
        }

        const start = new Date(task.scheduledStart);
        const date = start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

        // For full-day events, just show the date
        if (task.isFullDay || !task.scheduledEnd) {
            return `${date}, All day`;
        }

        // For time-specific events, show the time range
        const end = new Date(task.scheduledEnd);
        const startTime = start.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        const endTime = end.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        return `${date}, ${startTime} - ${endTime}`;
    };

    // Find task by ID (from both active and archived tasks)
    const findTaskById = (id) => {
        let task = tasks.find(t => t.id === id);
        if (!task) {
            task = archivedTasks.find(t => t.id === id);
        }
        return task;
    };

    // Update calendar events from tasks
    const updateCalendarEvents = () => {
        if (!calendar) return;

        console.log('Updating calendar events');

        // Format events for our custom calendar
        const calendarEvents = [];

        // Add scheduled tasks as events
        [...tasks, ...archivedTasks].forEach(task => {
            if (task.scheduled && task.scheduledStart) {
                // Create a proper date object
                const eventDate = new Date(task.scheduledStart);

                // Create the event object
                const eventObj = {
                    id: task.id,
                    title: task.title,
                    date: eventDate,
                    isFullDay: task.isFullDay || false,
                    archived: archivedTasks.some(t => t.id === task.id)
                };

                // Only add endTime for time-based events
                if (!task.isFullDay && task.scheduledEnd) {
                    eventObj.endTime = new Date(task.scheduledEnd);
                }

                console.log('Adding event to calendar:', eventObj);
                calendarEvents.push(eventObj);
            }
        });

        console.log('Total events to display:', calendarEvents.length);

        // Force a re-render of the calendar with the new events
        calendar.setEvents(calendarEvents);

        // Force an additional calendar render to ensure UI is updated
        if (calendar.render) {
            calendar.render();
        }

        // Schedule notifications for all tasks if notification manager is initialized
        if (notificationManager) {
            notificationManager.scheduleAllTaskNotifications(tasks, archivedTasks);
        }
    };

    // Setup calendar time cells as drop targets
    const setupCalendarDropTargets = () => {
        const timeSlots = document.querySelectorAll('.time-cell');

        timeSlots.forEach(cell => {
            // Make cells accept drops
            cell.addEventListener('dragover', (e) => {
                // Check if what's being dragged is a task
                const types = e.dataTransfer.types;
                if (types.includes('application/json')) {
                    e.preventDefault();
                    cell.classList.add('drag-over');
                }
            });

            cell.addEventListener('dragleave', () => {
                cell.classList.remove('drag-over');
            });

            cell.addEventListener('drop', (e) => {
                e.preventDefault();
                cell.classList.remove('drag-over');

                try {
                    const data = JSON.parse(e.dataTransfer.getData('application/json'));
                    // Check if the dragged item is a task from the list or an existing calendar event
                    if (data.type === 'task' || data.type === 'calendar-event') {
                        // Get the time slot's datetime from the cell's data-time attribute
                        const cellTime = cell.getAttribute('data-time');
                        const taskId = data.taskId;

                        // Find the task in both active and archived tasks
                        const taskActive = tasks.find(t => t.id === taskId);
                        const taskArchived = archivedTasks.find(t => t.id === taskId);
                        const task = taskActive || taskArchived;

                        if (task) {
                            // Calculate the duration to preserve
                            let durationMinutes = 30; // Default duration

                            if (task.scheduled && task.scheduledStart && task.scheduledEnd) {
                                // Calculate existing duration
                                const currentStart = new Date(task.scheduledStart);
                                const currentEnd = new Date(task.scheduledEnd);
                                durationMinutes = Math.round((currentEnd - currentStart) / 60000);
                            }

                            // Set the new start time
                            const newStartTime = new Date(cellTime);

                            // Calculate new end time based on preserved duration
                            const newEndTime = new Date(newStartTime.getTime() + durationMinutes * 60000);

                            // Update the task with new times
                            if (taskActive) {
                                const index = tasks.findIndex(t => t.id === taskId);
                                tasks[index].scheduled = true;
                                tasks[index].scheduledStart = newStartTime.toISOString();
                                tasks[index].scheduledEnd = newEndTime.toISOString();
                            } else if (taskArchived) {
                                const index = archivedTasks.findIndex(t => t.id === taskId);
                                archivedTasks[index].scheduled = true;
                                archivedTasks[index].scheduledStart = newStartTime.toISOString();
                                archivedTasks[index].scheduledEnd = newEndTime.toISOString();
                            }

                            // Save changes
                            saveTasks();

                            // Remove the dragged element immediately to avoid visual artifacts
                            const draggedElement = document.querySelector(`.time-event[data-id="${taskId}"]`);
                            if (draggedElement) {
                                draggedElement.remove();
                            }

                            // Force an immediate calendar refresh
                            calendar.render();
                            updateCalendarEvents();

                            // Refresh the task list
                            renderTasks();
                            if (showArchived) {
                                renderArchivedTasks();
                            }

                            console.log('Task rescheduled with preserved duration:', durationMinutes, 'minutes');
                        }
                    }
                } catch (error) {
                    console.error('Error processing drop:', error);
                }
            });
        });
    };

    // Make the task list a drop target for unscheduling tasks
    const setupTaskListDropTarget = () => {
        // Target both the task list container and the list itself
        const taskListContainer = document.querySelector('.task-list-container');
        const taskList = document.getElementById('task-list');

        // Helper to clear all drop indicators throughout the UI
        const clearAllDropIndicators = () => {
            // Clear task list container indicator
            document.querySelectorAll('.drag-target').forEach(el => {
                el.classList.remove('drag-target');
            });

            // Clear individual task item indicators
            document.querySelectorAll('.calendar-drop-target').forEach(el => {
                el.classList.remove('calendar-drop-target');
            });

            // Clear calendar cell indicators
            document.querySelectorAll('.drag-over').forEach(el => {
                el.classList.remove('drag-over');
            });
        };

        const setupDropTarget = (element) => {
            // Set up the element as a drop target
            element.addEventListener('dragover', (e) => {
                // Accept all drags - we'll check the type on drop
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                taskListContainer.classList.add('drag-target');
            });

            element.addEventListener('dragleave', (e) => {
                // Only remove the class if we're leaving the container, not just moving between child elements
                const rect = element.getBoundingClientRect();
                if (e.clientX < rect.left || e.clientX >= rect.right ||
                    e.clientY < rect.top || e.clientY >= rect.bottom) {
                    taskListContainer.classList.remove('drag-target');
                }
            });

            element.addEventListener('drop', (e) => {
                e.preventDefault();
                e.stopPropagation(); // Prevent bubbling

                // Clear all drop indicators
                clearAllDropIndicators();

                let data;
                try {
                    data = JSON.parse(e.dataTransfer.getData('application/json'));
                } catch (error) {
                    console.log('No JSON data found in drop');
                    return;
                }

                console.log('Drop data received:', data);

                // If this is a calendar event being dropped onto the task list, unschedule it
                if (data && (data.type === 'calendar-task' || data.type === 'full-day-event' || data.type === 'calendar-event') && data.taskId) {
                    const taskIndex = tasks.findIndex(task => task.id === data.taskId);
                    const archivedTaskIndex = archivedTasks.findIndex(task => task.id === data.taskId);
                    if (taskIndex !== -1) {
                        tasks[taskIndex].scheduled = false;
                        tasks[taskIndex].scheduledStart = null;
                        tasks[taskIndex].scheduledEnd = null;
                        tasks[taskIndex].isFullDay = false;
                        saveTasks();
                        renderTasks();
                        updateCalendarEvents();
                        if (calendar) {
                            calendar.render();
                        }
                        console.log('Task unscheduled and moved to to-do list:', data.taskId);
                    } else if (archivedTaskIndex !== -1) {
                        archivedTasks[archivedTaskIndex].scheduled = false;
                        archivedTasks[archivedTaskIndex].scheduledStart = null;
                        archivedTasks[archivedTaskIndex].scheduledEnd = null;
                        archivedTasks[archivedTaskIndex].isFullDay = false;
                        saveTasks();
                        renderArchivedTasks();
                        updateCalendarEvents();
                        if (calendar) {
                            calendar.render();
                        }
                        console.log('Archived task unscheduled and moved to to-do list:', data.taskId);
                    }
                }
            });
        };
        // Set up both elements as drop targets
        setupDropTarget(taskListContainer);
        setupDropTarget(taskList);

        // Also listen for dragend events globally to clear indicators
        document.addEventListener('dragend', () => {
            clearAllDropIndicators();
        });
    };

    // Setup Google Calendar UI
    const setupGoogleCalendarUI = () => {
        const signInBtn = document.getElementById('google-sign-in-btn');
        const signOutBtn = document.getElementById('google-sign-out-btn');
        const calendarToggle = document.getElementById('google-calendar-enabled');
        const syncBtn = document.getElementById('google-calendar-sync-btn');

        // Sign in button
        signInBtn.addEventListener('click', async () => {
            try {
                signInBtn.disabled = true;
                signInBtn.textContent = 'Signing in...';

                console.log('Starting Google sign-in process...');
                const signInResult = await googleAuthService.signIn();

                if (!signInResult) {
                    throw new Error('Sign-in was cancelled or failed');
                }

                console.log('Google sign-in successful, validating and updating state...');

                // Validate and fix any authentication state inconsistencies
                const authStatus = await googleAuthService.validateAndFixAuthState();

                // Update UI
                await updateGoogleAuthUI();

                // Small delay to ensure state propagation
                await new Promise(resolve => setTimeout(resolve, 100));

                // More lenient validation - if we have a token and are marked as authenticated, proceed
                const isValidAuth = authStatus.isAuthenticated && authStatus.hasAccessToken;
                const tokenExpiredButRecent = authStatus.isTokenExpired && authStatus.tokenExpiry &&
                    (Date.now() - authStatus.tokenExpiry < 60000); // Token expired less than 1 minute ago

                if (isValidAuth && (!authStatus.isTokenExpired || tokenExpiredButRecent)) {
                    console.log('Authentication verified, enabling Google Calendar...');
                    // Enable calendar integration by default after successful sign-in
                    if (googleCalendarService) {
                        try {
                            console.log('Attempting to enable Google Calendar with auth verification...');
                            await googleCalendarService.enableWithAuthVerification();
                            calendarToggle.checked = true;
                            updateGoogleCalendarUI();
                            console.log('Google Calendar integration enabled successfully');
                            showNotification('Successfully signed in and enabled Google Calendar integration', 'success');
                        } catch (calendarError) {
                            console.error('Failed to enable Google Calendar:', calendarError);
                            console.error('Calendar error details:', {
                                message: calendarError.message,
                                stack: calendarError.stack
                            });
                            showNotification('Signed in successfully, but failed to enable calendar integration: ' + calendarError.message, 'warning');
                        }
                    }
                } else {
                    console.error('Authentication state validation failed:', {
                        isAuthenticated: authStatus.isAuthenticated,
                        hasAccessToken: authStatus.hasAccessToken,
                        isTokenExpired: authStatus.isTokenExpired,
                        tokenExpiry: authStatus.tokenExpiry,
                        hasUserInfo: !!authStatus.userInfo,
                        isValidAuth: isValidAuth,
                        tokenExpiredButRecent: tokenExpiredButRecent
                    });

                    // Provide more specific error messages
                    let errorMessage = 'Sign-in completed but authentication state is invalid: ';
                    if (!authStatus.isAuthenticated) {
                        errorMessage += 'Not marked as authenticated';
                    } else if (!authStatus.hasAccessToken) {
                        errorMessage += 'No access token available';
                    } else if (authStatus.isTokenExpired && !tokenExpiredButRecent) {
                        errorMessage += 'Token is expired';
                    } else {
                        errorMessage += 'Unknown validation issue';
                    }

                    showNotification(errorMessage, 'warning');
                }
            } catch (error) {
                console.error('Google sign-in failed:', error);
                showNotification('Failed to sign in to Google: ' + error.message, 'error');
            } finally {
                signInBtn.disabled = false;
                signInBtn.textContent = 'Sign in with Google';
            }
        });

        // Sign out button
        signOutBtn.addEventListener('click', async () => {
            try {
                await googleAuthService.signOut();
                if (googleCalendarService) {
                    await googleCalendarService.disable();
                }
                await updateGoogleAuthUI();
                updateGoogleCalendarUI();
                updateCalendarWithGoogleEvents(); // Clear Google events from calendar
            } catch (error) {
                console.error('Google sign-out failed:', error);
                showNotification('Failed to sign out: ' + error.message, 'error');
            }
        });

        // Calendar toggle
        calendarToggle.addEventListener('change', async (e) => {
            if (!googleAuthService.isAuthenticated) {
                e.target.checked = false;
                return;
            }

            try {
                if (e.target.checked) {
                    await googleCalendarService.enable();
                } else {
                    await googleCalendarService.disable();
                    updateCalendarWithGoogleEvents(); // Clear Google events
                }
                updateGoogleCalendarUI();
            } catch (error) {
                console.error('Failed to toggle Google Calendar:', error);
                showNotification('Failed to toggle Google Calendar: ' + error.message, 'error');
                e.target.checked = !e.target.checked; // Revert toggle
            }
        });

        // Sync button
        syncBtn.addEventListener('click', async () => {
            try {
                syncBtn.disabled = true;
                syncBtn.textContent = 'Syncing...';

                await googleCalendarService.forcSync();
                updateGoogleCalendarUI();
                showNotification('Google Calendar synced successfully', 'success');
            } catch (error) {
                console.error('Google Calendar sync failed:', error);
                showNotification('Failed to sync Google Calendar: ' + error.message, 'error');
            } finally {
                syncBtn.disabled = false;
                syncBtn.textContent = 'Sync now';
            }
        });

        // Initial UI update will be handled after initialization
    };

    // Update Google authentication UI
    const updateGoogleAuthUI = async () => {
        const authStatus = document.getElementById('google-auth-status');
        const userInfo = document.getElementById('google-user-info');
        const signInBtn = document.getElementById('google-sign-in-btn');
        const signOutBtn = document.getElementById('google-sign-out-btn');
        const toggleSection = document.getElementById('google-calendar-toggle-section');
        const syncSection = document.getElementById('google-calendar-sync-section');

        if (!googleAuthService) {
            console.log('Google Auth Service not available');
            return;
        }

        // Wait for initialization if needed
        if (!googleAuthService.isInitialized) {
            console.log('Waiting for Google Auth Service initialization...');
            await googleAuthService.waitForInitialization();
        }

        // Perform lazy token verification if needed
        await googleAuthService.verifyTokenIfNeeded();

        const authStatusData = googleAuthService.getAuthStatus();
        console.log('Updating Google Auth UI with status:', authStatusData);

        if (authStatusData.isAuthenticated && !authStatusData.isTokenExpired) {
            authStatus.querySelector('.status-text').textContent = 'Connected';
            authStatus.className = 'auth-status connected';

            if (authStatusData.userInfo) {
                userInfo.textContent = authStatusData.userInfo.email;
                userInfo.style.display = 'inline';
            } else {
                userInfo.style.display = 'none';
            }

            signInBtn.style.display = 'none';
            signOutBtn.style.display = 'inline-block';
            toggleSection.style.display = 'block';
            syncSection.style.display = 'block';
        } else {
            authStatus.querySelector('.status-text').textContent = 'Not connected';
            authStatus.className = 'auth-status';
            userInfo.style.display = 'none';

            signInBtn.style.display = 'inline-block';
            signOutBtn.style.display = 'none';
            toggleSection.style.display = 'none';
            syncSection.style.display = 'none';
        }
    };

    // Restore Google Calendar state if authentication was restored
    const restoreGoogleCalendarState = async () => {
        if (!googleCalendarService || !googleAuthService) return;

        const authStatus = googleAuthService.getAuthStatus();
        if (!authStatus.isAuthenticated || authStatus.isTokenExpired) return;

        // Check if calendar was previously enabled
        const calendarStatus = googleCalendarService.getSyncStatus();
        if (calendarStatus.isEnabled) {
            console.log('Restoring Google Calendar integration...');
            try {
                // Re-enable calendar integration and sync
                await googleCalendarService.enable();
                updateGoogleCalendarUI();
                console.log('Google Calendar integration restored');
            } catch (error) {
                console.error('Failed to restore Google Calendar integration:', error);
            }
        }
    };

    // Update Google Calendar UI
    const updateGoogleCalendarUI = () => {
        if (!googleCalendarService) return;

        const calendarToggle = document.getElementById('google-calendar-enabled');
        const lastSyncEl = document.getElementById('google-calendar-last-sync');
        const eventCountEl = document.getElementById('google-calendar-event-count');

        const status = googleCalendarService.getSyncStatus();

        calendarToggle.checked = status.isEnabled;

        if (status.lastSyncTime) {
            const syncDate = new Date(status.lastSyncTime);
            lastSyncEl.textContent = syncDate.toLocaleString();
        } else {
            lastSyncEl.textContent = 'Never';
        }

        eventCountEl.textContent = status.eventCount.toString();
    };

    // Update calendar with Google events
    const updateCalendarWithGoogleEvents = () => {
        if (!calendar || !googleCalendarService) return;

        const googleEvents = googleCalendarService.getEvents();
        calendar.setGoogleEvents(googleEvents);
    };

    // Show Google event info
    const showGoogleEventInfo = (event) => {
        const message = `
            <strong>${event.title}</strong><br>
            ${event.isFullDay ? 'All day' : `${formatTime(event.date)} - ${formatTime(event.endTime)}`}<br>
            ${event.location ? `Location: ${event.location}<br>` : ''}
            ${event.description ? `Description: ${event.description}<br>` : ''}
            <em>From Google Calendar (read-only)</em>
        `;
        showNotification(message, 'info', 5000);
    };

    // Show notification helper
    const showNotification = (message, type = 'info', duration = 3000) => {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
            </div>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;

        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Add event listener to close button
        notification.querySelector('.close-notification').addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });

        // Auto-hide after duration
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.remove();
                    }
                }, 300);
            }
        }, duration);
    };

    // Format time helper
    const formatTime = (date) => {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    // Initialize
    const initialize = async () => {
        loadTasks();
        initializeCalendar();
        setupTaskListDropTarget();
        initializeNotifications();
        await initializeGoogleCalendar();
    };

    // Start initialization
    initialize().catch(error => {
        console.error('Error during initialization:', error);
    });

    // Set up mutation observer to watch for calendar rendering
    const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if time cells were added
                const timeCells = document.querySelectorAll('.time-cell');
                if (timeCells.length > 0) {
                    setupCalendarDropTargets();
                    break;
                }
            }
        }
    });

    // Start observing calendar body for changes
    observer.observe(document.getElementById('calendar-body'), {
        childList: true,
        subtree: true
    });
});
