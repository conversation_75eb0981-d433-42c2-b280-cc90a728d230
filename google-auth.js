/**
 * Google Authentication Service for CalenTask Chrome Extension
 * Handles OAuth 2.0 authentication with Google APIs
 */
class GoogleAuthService {
  constructor() {
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;
    this.isInitialized = false;
    this.initializationPromise = null;

    // Initialize authentication state from storage
    this.initializationPromise = this.loadAuthState();
  }

  /**
   * Load authentication state from Chrome storage
   */
  async loadAuthState() {
    try {
      console.log('Loading authentication state from storage...');
      const result = await chrome.storage.local.get([
        'google_access_token',
        'google_refresh_token',
        'google_token_expiry',
        'google_user_info',
        'google_auth_enabled'
      ]);

      this.accessToken = result.google_access_token || null;
      this.refreshToken = result.google_refresh_token || null;
      this.tokenExpiry = result.google_token_expiry || null;
      this.userInfo = result.google_user_info || null;
      this.isAuthenticated = result.google_auth_enabled || false;

      console.log('Loaded auth state:', {
        hasToken: !!this.accessToken,
        isAuthenticated: this.isAuthenticated,
        tokenExpiry: this.tokenExpiry,
        hasUserInfo: !!this.userInfo
      });

      // Check if we have a token but it's expired
      if (this.accessToken && this.isTokenExpired()) {
        console.log('Token is expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();
        if (!refreshed) {
          console.log('Token refresh failed, clearing auth state');
          await this.clearAuthState();
        }
      } else if (this.accessToken && this.isAuthenticated) {
        // For initial load, trust the stored authentication state
        // Token verification will happen on first API call if needed
        console.log('Found stored authentication state, trusting it for now');
        console.log('Token verification will be performed on first API usage');
      }

      this.isInitialized = true;
      console.log('Authentication initialization complete. Final state:', {
        isAuthenticated: this.isAuthenticated,
        hasValidToken: !!this.accessToken && !this.isTokenExpired()
      });
    } catch (error) {
      console.error('Error loading auth state:', error);
      await this.clearAuthState();
      this.isInitialized = true;
    }
  }

  /**
   * Wait for authentication initialization to complete
   */
  async waitForInitialization() {
    if (this.isInitialized) {
      return;
    }
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  /**
   * Verify that the current token is still valid
   */
  async verifyToken() {
    if (!this.accessToken) {
      return false;
    }

    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        // Update user info if the token is valid
        const userInfo = await response.json();
        if (userInfo) {
          this.userInfo = userInfo;
          await this.saveAuthState();
        }
        return true;
      } else {
        console.log('Token verification failed with status:', response.status);
        return false;
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      return false;
    }
  }

  /**
   * Perform a lazy verification of the token (only if needed)
   * This is called when the UI needs to ensure the auth state is accurate
   */
  async verifyTokenIfNeeded() {
    if (!this.accessToken || !this.isAuthenticated) {
      return false;
    }

    // If we don't have user info, try to fetch it to verify the token
    if (!this.userInfo) {
      console.log('No user info found, verifying token...');
      const isValid = await this.verifyToken();
      if (!isValid) {
        console.log('Token verification failed, clearing auth state');
        await this.clearAuthState();
        return false;
      }
    }

    return true;
  }

  /**
   * Save authentication state to Chrome storage
   */
  async saveAuthState() {
    try {
      await chrome.storage.local.set({
        google_access_token: this.accessToken,
        google_refresh_token: this.refreshToken,
        google_token_expiry: this.tokenExpiry,
        google_user_info: this.userInfo,
        google_auth_enabled: this.isAuthenticated
      });
      console.log('Authentication state saved to storage');
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState() {
    console.log('Clearing authentication state');
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;

    try {
      await chrome.storage.local.remove([
        'google_access_token',
        'google_refresh_token',
        'google_token_expiry',
        'google_user_info',
        'google_auth_enabled'
      ]);
      console.log('Authentication state cleared from storage');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  /**
   * Check if the current access token is expired
   */
  isTokenExpired() {
    if (!this.tokenExpiry) {
      console.log('Token expiry not set, considering token expired');
      return true;
    }
    const isExpired = Date.now() >= this.tokenExpiry;
    console.log('Token expiry check:', {
      tokenExpiry: this.tokenExpiry,
      currentTime: Date.now(),
      isExpired: isExpired,
      timeUntilExpiry: this.tokenExpiry - Date.now()
    });
    return isExpired;
  }

  /**
   * Sign in with Google using Chrome Identity API
   */
  async signIn() {
    try {
      // Clear any existing auth state first
      await this.clearAuthState();

      // Use Chrome Identity API's launchWebAuthFlow method
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com'; // Your client ID
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';

      // Build the authorization URL
      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);

      console.log('Using auth URL:', authURL.toString());
      console.log('Redirect URL:', redirectURL);

      // Launch the web auth flow
      const responseURL = await chrome.identity.launchWebAuthFlow({
        url: authURL.toString(),
        interactive: true
      });

      if (responseURL) {
        console.log('OAuth response URL received:', responseURL);
        // Extract access token from the response URL
        const url = new URL(responseURL);
        const params = new URLSearchParams(url.hash.substring(1));
        this.accessToken = params.get('access_token');
        const expiresIn = params.get('expires_in');

        console.log('OAuth response parameters:', {
          hasAccessToken: !!this.accessToken,
          expiresIn: expiresIn,
          allParams: Object.fromEntries(params.entries())
        });

        if (this.accessToken) {
          console.log('Access token received, setting authentication state...');

          // Set token expiry - default to 1 hour if not provided
          if (expiresIn) {
            this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
          } else {
            console.warn('No expires_in parameter found, defaulting to 1 hour');
            this.tokenExpiry = Date.now() + (3600 * 1000); // 1 hour default
          }

          // Set authentication state AFTER we have the token and expiry
          this.isAuthenticated = true;

          console.log('Core authentication state set:', {
            isAuthenticated: this.isAuthenticated,
            hasAccessToken: !!this.accessToken,
            tokenExpiry: this.tokenExpiry,
            expiresInSeconds: expiresIn
          });

          // Try to get user info first (this validates the token)
          try {
            console.log('Attempting to fetch user info to validate token...');
            await this.fetchUserInfo();
            if (this.userInfo) {
              console.log('User info fetched successfully:', this.userInfo.email);
            } else {
              console.warn('Failed to fetch user info, but continuing with authentication');
            }
          } catch (userInfoError) {
            console.error('Error fetching user info (authentication may still be valid):', userInfoError);
            // Don't fail the authentication just because user info fetch failed
          }

          // Save the complete authentication state
          try {
            await this.saveAuthState();
            console.log('Authentication state saved successfully');

            // Verify the state was saved correctly by reading it back
            const savedState = await chrome.storage.local.get([
              'google_access_token',
              'google_auth_enabled'
            ]);
            console.log('Verification - saved state:', {
              hasToken: !!savedState.google_access_token,
              isEnabled: savedState.google_auth_enabled
            });

          } catch (saveError) {
            console.error('Failed to save authentication state:', saveError);
            // This is critical - if we can't save state, the auth won't persist
            throw new Error('Failed to save authentication state');
          }

          console.log('Google sign-in successful - final auth state:', {
            isAuthenticated: this.isAuthenticated,
            hasAccessToken: !!this.accessToken,
            hasUserInfo: !!this.userInfo,
            tokenExpiry: this.tokenExpiry
          });

          return true;
        } else {
          console.error('No access token found in OAuth response');
          return false;
        }
      } else {
        console.error('No response URL received from OAuth flow');
      }
    } catch (error) {
      console.error('Google sign-in failed:', error);
      await this.clearAuthState();
      throw new Error(`Authentication failed: ${error.message}`);
    }

    return false;
  }

  /**
   * Sign out from Google
   */
  async signOut() {
    try {
      if (this.accessToken) {
        // Revoke the token
        await chrome.identity.removeCachedAuthToken({ token: this.accessToken });
      }

      await this.clearAuthState();
      console.log('Google sign-out successful');
      return true;
    } catch (error) {
      console.error('Google sign-out failed:', error);
      // Clear state anyway
      await this.clearAuthState();
      return false;
    }
  }

  /**
   * Refresh the access token
   */
  async refreshAccessToken() {
    try {
      // For the launchWebAuthFlow approach, we need to re-authenticate
      // as refresh tokens aren't typically used with this flow
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com';
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';

      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);

      // Non-interactive refresh attempt
      try {
        const responseURL = await chrome.identity.launchWebAuthFlow({
          url: authURL.toString(),
          interactive: false
        });

        if (responseURL) {
          const url = new URL(responseURL);
          const params = new URLSearchParams(url.hash.substring(1));
          this.accessToken = params.get('access_token');
          const expiresIn = params.get('expires_in');

          if (this.accessToken) {
            this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
            await this.saveAuthState();
            return true;
          }
        }
      } catch (nonInteractiveError) {
        console.log('Non-interactive token refresh failed, will try interactive');
        // Fall through to interactive auth
      }

      // If non-interactive refresh fails, try interactive
      return await this.signIn();
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearAuthState();
      return false;
    }
  }

  /**
   * Fetch user information from Google API
   */
  async fetchUserInfo() {
    if (!this.accessToken) return null;

    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        this.userInfo = await response.json();
        return this.userInfo;
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
    }

    return null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   */
  async getValidToken() {
    if (!this.isAuthenticated || !this.accessToken) {
      throw new Error('Not authenticated');
    }

    if (this.isTokenExpired()) {
      const refreshed = await this.refreshAccessToken();
      if (!refreshed) {
        throw new Error('Failed to refresh token');
      }
    }

    return this.accessToken;
  }

  /**
   * Make an authenticated API request to Google
   */
  async makeAuthenticatedRequest(url, options = {}) {
    const token = await this.getValidToken();

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (response.status === 401) {
      console.log('API request returned 401, attempting token refresh...');
      // Token might be invalid, try to refresh
      const refreshed = await this.refreshAccessToken();
      if (refreshed) {
        console.log('Token refresh successful, retrying request...');
        // Retry the request with new token
        const newToken = await this.getValidToken();
        headers['Authorization'] = `Bearer ${newToken}`;
        return fetch(url, { ...options, headers });
      } else {
        console.log('Token refresh failed, clearing auth state');
        await this.clearAuthState();
        throw new Error('Authentication failed');
      }
    }

    return response;
  }

  /**
   * Get authentication status (synchronous, may not be fully initialized)
   */
  getAuthStatus() {
    // Ensure we have the most current state
    const status = {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      tokenExpiry: this.tokenExpiry,
      isTokenExpired: this.isTokenExpired(),
      isInitialized: this.isInitialized,
      hasAccessToken: !!this.accessToken,
      accessToken: this.accessToken // Include for debugging (will be logged)
    };

    console.log('getAuthStatus() returning:', {
      isAuthenticated: status.isAuthenticated,
      hasAccessToken: status.hasAccessToken,
      isTokenExpired: status.isTokenExpired,
      isInitialized: status.isInitialized,
      hasUserInfo: !!status.userInfo
    });

    return status;
  }

  /**
   * Get authentication status after waiting for initialization
   */
  async getAuthStatusAsync() {
    await this.waitForInitialization();
    return this.getAuthStatus();
  }

  /**
   * Force refresh the authentication state (useful after sign-in)
   */
  async refreshAuthState() {
    console.log('Refreshing authentication state...');
    const currentStatus = this.getAuthStatus();
    console.log('Current auth state before refresh:', currentStatus);

    // Validate basic authentication requirements
    if (!this.accessToken) {
      console.error('No access token available');
      return this.getAuthStatus();
    }

    if (!this.isAuthenticated) {
      console.warn('isAuthenticated flag is false, but we have an access token - fixing this');
      this.isAuthenticated = true;
      await this.saveAuthState();
    }

    // Check token expiry
    if (!this.tokenExpiry) {
      console.warn('Token expiry not set, setting default expiry');
      this.tokenExpiry = Date.now() + (3600 * 1000); // 1 hour default
      await this.saveAuthState();
    }

    // If we have a token but no user info, try to fetch it
    if (!this.userInfo) {
      console.log('Fetching user info to complete authentication state...');
      try {
        await this.fetchUserInfo();
        if (this.userInfo) {
          console.log('User info fetched successfully:', this.userInfo.email);
        } else {
          console.warn('Failed to fetch user info, but continuing with authentication');
        }
        await this.saveAuthState();
      } catch (error) {
        console.error('Error fetching user info:', error);
        // Don't fail the authentication just because user info fetch failed
      }
    }

    const newStatus = this.getAuthStatus();
    console.log('Auth state after refresh:', newStatus);

    // Validate final state
    const isValid = newStatus.isAuthenticated && !newStatus.isTokenExpired;
    console.log('Authentication state validation result:', isValid);

    return newStatus;
  }

  /**
   * Validate and fix authentication state inconsistencies
   */
  async validateAndFixAuthState() {
    console.log('Validating authentication state...');

    // First, ensure we're initialized
    await this.waitForInitialization();

    const currentState = {
      hasAccessToken: !!this.accessToken,
      isAuthenticated: this.isAuthenticated,
      hasTokenExpiry: !!this.tokenExpiry,
      hasUserInfo: !!this.userInfo
    };
    console.log('Current state before validation:', currentState);

    let stateFixed = false;

    // If we have an access token but isAuthenticated is false, fix it
    if (this.accessToken && !this.isAuthenticated) {
      console.log('Fixing: Have access token but isAuthenticated is false');
      this.isAuthenticated = true;
      stateFixed = true;
    }

    // If we have an access token but no token expiry, set a default
    if (this.accessToken && !this.tokenExpiry) {
      console.log('Fixing: Have access token but no token expiry');
      this.tokenExpiry = Date.now() + (3600 * 1000); // 1 hour default
      stateFixed = true;
    }

    // If we're marked as authenticated but have no token, clear the auth state
    if (this.isAuthenticated && !this.accessToken) {
      console.log('Fixing: Marked as authenticated but no access token - clearing auth state');
      await this.clearAuthState();
      stateFixed = true;
    }

    // Save the fixed state
    if (stateFixed) {
      console.log('Authentication state was fixed, saving...');
      await this.saveAuthState();

      // Verify the save worked
      const verifyState = await chrome.storage.local.get([
        'google_access_token',
        'google_auth_enabled'
      ]);
      console.log('Verification after fix - saved state:', {
        hasToken: !!verifyState.google_access_token,
        isEnabled: verifyState.google_auth_enabled
      });
    }

    const finalState = this.getAuthStatus();
    console.log('Final authentication state after validation:', {
      isAuthenticated: finalState.isAuthenticated,
      hasAccessToken: finalState.hasAccessToken,
      isTokenExpired: finalState.isTokenExpired,
      isInitialized: finalState.isInitialized
    });

    return finalState;
  }
}

// Export for use in other modules
window.GoogleAuthService = GoogleAuthService;
