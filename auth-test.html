<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .primary {
            background-color: #007bff;
            color: white;
        }
        .secondary {
            background-color: #6c757d;
            color: white;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Google Authentication Test</h1>
    <p>This page tests the persistent authentication functionality.</p>

    <div id="status" class="status loading">
        <strong>Status:</strong> <span id="status-text">Initializing...</span>
    </div>

    <div id="user-info" style="display: none;">
        <p><strong>User:</strong> <span id="user-email"></span></p>
        <p><strong>Token Expiry:</strong> <span id="token-expiry"></span></p>
    </div>

    <div id="controls">
        <button id="sign-in-btn" class="primary">Sign In</button>
        <button id="sign-out-btn" class="secondary" style="display: none;">Sign Out</button>
        <button id="refresh-btn" class="secondary">Refresh Status</button>
        <button id="clear-log-btn" class="secondary">Clear Log</button>
    </div>

    <h3>Instructions:</h3>
    <ol>
        <li>Click "Sign In" to authenticate with Google</li>
        <li>After successful authentication, refresh this page</li>
        <li>Check if you remain logged in (status should show "Connected")</li>
        <li>Check the log for detailed authentication flow information</li>
        <li><strong>Expected behavior after fix:</strong> Status should show "Connected" immediately after page refresh without requiring any user interaction</li>
    </ol>

    <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <strong>Latest Fix Applied:</strong> Fixed timing issue between GoogleAuthService and GoogleCalendarService. The calendar service now properly verifies authentication state before enabling, eliminating "Must be authenticated to enable Google Calendar" errors.
    </div>

    <h3>Log:</h3>
    <div id="log" class="log"></div>

    <script src="google-auth.js"></script>
    <script>
        let authService;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateUI() {
            const status = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            const userInfo = document.getElementById('user-info');
            const userEmail = document.getElementById('user-email');
            const tokenExpiry = document.getElementById('token-expiry');
            const signInBtn = document.getElementById('sign-in-btn');
            const signOutBtn = document.getElementById('sign-out-btn');

            if (!authService) {
                status.className = 'status disconnected';
                statusText.textContent = 'Auth service not available';
                return;
            }

            const authStatus = authService.getAuthStatus();
            log(`Auth status: ${JSON.stringify(authStatus, null, 2)}`);

            // Use the same validation logic as the main app
            const isValidAuth = authStatus.isAuthenticated && authStatus.hasAccessToken;
            const tokenExpiredButRecent = authStatus.isTokenExpired && authStatus.tokenExpiry &&
                (Date.now() - authStatus.tokenExpiry < 60000);

            log(`Validation: isValidAuth=${isValidAuth}, tokenExpiredButRecent=${tokenExpiredButRecent}`);

            if (isValidAuth && (!authStatus.isTokenExpired || tokenExpiredButRecent)) {
                status.className = 'status connected';
                statusText.textContent = 'Connected';

                if (authStatus.userInfo) {
                    userEmail.textContent = authStatus.userInfo.email;
                    userInfo.style.display = 'block';
                }

                if (authStatus.tokenExpiry) {
                    const expiry = new Date(authStatus.tokenExpiry);
                    tokenExpiry.textContent = expiry.toLocaleString();
                }

                signInBtn.style.display = 'none';
                signOutBtn.style.display = 'inline-block';
            } else {
                status.className = 'status disconnected';
                statusText.textContent = 'Not connected';
                userInfo.style.display = 'none';
                signInBtn.style.display = 'inline-block';
                signOutBtn.style.display = 'none';
            }
        }

        async function initialize() {
            log('Initializing Google Auth Service...');

            if (!window.GoogleAuthService) {
                log('ERROR: GoogleAuthService not available');
                return;
            }

            authService = new GoogleAuthService();
            log('Auth service created, waiting for initialization...');

            await authService.waitForInitialization();
            log('Auth service initialization complete');

            updateUI();
        }

        // Event listeners
        document.getElementById('sign-in-btn').addEventListener('click', async () => {
            try {
                log('Starting sign-in process...');
                const result = await authService.signIn();
                log(`Sign-in result: ${result}`);

                if (result) {
                    log('Sign-in successful, validating auth state...');
                    const authStatus = await authService.validateAndFixAuthState();
                    log(`Auth status after validation: ${JSON.stringify(authStatus, null, 2)}`);
                    log('Sign-in process completed successfully');
                } else {
                    log('Sign-in returned false - may have been cancelled');
                }

                updateUI();
            } catch (error) {
                log(`Sign-in failed: ${error.message}`);
            }
        });

        document.getElementById('sign-out-btn').addEventListener('click', async () => {
            try {
                log('Starting sign-out process...');
                await authService.signOut();
                log('Sign-out successful');
                updateUI();
            } catch (error) {
                log(`Sign-out failed: ${error.message}`);
            }
        });

        document.getElementById('refresh-btn').addEventListener('click', () => {
            log('Refreshing status...');
            updateUI();
        });

        document.getElementById('clear-log-btn').addEventListener('click', () => {
            document.getElementById('log').innerHTML = '';
        });

        // Initialize when page loads
        window.addEventListener('load', () => {
            log('Page loaded, starting initialization...');
            initialize().catch(error => {
                log(`Initialization failed: ${error.message}`);
            });
        });
    </script>
</body>
</html>
