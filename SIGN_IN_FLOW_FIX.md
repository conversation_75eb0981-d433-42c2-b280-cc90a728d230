# Google Sign-In Flow Fix

## Issue Description

After implementing authentication persistence fixes, users encountered multiple errors during the sign-in process:

1. **First Error**: "Failed to sign in to Google: Must be authenticated to enable Google Calendar"
2. **Second Error**: "Sign-in completed but authentication state is invalid"

The second error occurred after fixing the first one, indicating that while the OAuth flow completed successfully, the authentication state validation was failing.

## Root Cause Analysis

### First Error: Timing Issue
The first error occurred due to a **timing issue in the sign-in flow sequence**:
1. `googleAuthService.signIn()` completes successfully ✅
2. `googleCalendarService.enable()` is called immediately ❌
3. Calendar service checks authentication before state is fully propagated ❌

### Second Error: Token Expiry Validation Issue
The second error occurred due to **overly strict token validation**:
1. OAuth flow completes and returns access token ✅
2. But `expires_in` parameter might be missing or malformed ❌
3. `tokenExpiry` is not set properly ❌
4. `isTokenExpired()` returns `true` because `tokenExpiry` is null ❌
5. Validation `isAuthenticated && !isTokenExpired` fails ❌

The core issue was that the `isTokenExpired()` method returns `true` when `tokenExpiry` is null, which can happen if the OAuth response doesn't include the `expires_in` parameter or if there are parsing issues.

## The Fix

### 1. Enhanced Sign-In Flow Sequence

**Before (problematic)**:
```javascript
await googleAuthService.signIn();
await updateGoogleAuthUI();
// Immediately try to enable calendar - FAILS!
await googleCalendarService.enable();
```

**After (fixed)**:
```javascript
const signInResult = await googleAuthService.signIn();
if (!signInResult) {
    throw new Error('Sign-in was cancelled or failed');
}

await updateGoogleAuthUI();

// NEW: Refresh authentication state to ensure it's fully updated
const authStatus = await googleAuthService.refreshAuthState();

// NEW: Verify authentication before enabling calendar
if (authStatus.isAuthenticated && !authStatus.isTokenExpired) {
    await googleCalendarService.enable();
}
```

### 2. Enhanced Token Expiry Handling

**Improved `isTokenExpired()` method**:
```javascript
isTokenExpired() {
    if (!this.tokenExpiry) {
        console.log('Token expiry not set, considering token expired');
        return true;
    }
    const isExpired = Date.now() >= this.tokenExpiry;
    console.log('Token expiry check:', {
        tokenExpiry: this.tokenExpiry,
        currentTime: Date.now(),
        isExpired: isExpired
    });
    return isExpired;
}
```

**Enhanced sign-in method with fallback expiry**:
```javascript
// Set token expiry - default to 1 hour if not provided
if (expiresIn) {
    this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
} else {
    console.warn('No expires_in parameter found, defaulting to 1 hour');
    this.tokenExpiry = Date.now() + (3600 * 1000); // 1 hour default
}
```

### 3. Robust Authentication State Refresh Method

**Enhanced `refreshAuthState()` method**:
```javascript
async refreshAuthState() {
    // Validate basic authentication requirements
    if (!this.accessToken) {
        console.error('No access token available');
        return this.getAuthStatus();
    }

    // Check token expiry and set default if missing
    if (!this.tokenExpiry) {
        console.warn('Token expiry not set, setting default expiry');
        this.tokenExpiry = Date.now() + (3600 * 1000);
        await this.saveAuthState();
    }

    // Fetch user info if missing
    if (!this.userInfo) {
        await this.fetchUserInfo();
        await this.saveAuthState();
    }

    return this.getAuthStatus();
}
```

### 4. More Lenient Validation Logic

**New validation approach**:
```javascript
// More lenient validation - if we have a token and are marked as authenticated, proceed
const isValidAuth = authStatus.isAuthenticated && authStatus.hasAccessToken;
const tokenExpiredButRecent = authStatus.isTokenExpired && authStatus.tokenExpiry &&
    (Date.now() - authStatus.tokenExpiry < 60000); // Token expired less than 1 minute ago

if (isValidAuth && (!authStatus.isTokenExpired || tokenExpiredButRecent)) {
    // Proceed with calendar enablement
}
```

### 3. Enhanced Error Handling and Logging

**Improved sign-in button handler**:
- Added comprehensive logging at each step
- Proper verification of sign-in result
- Separate error handling for calendar enablement
- User-friendly success/warning notifications

**Enhanced Google Calendar service**:
- Detailed logging of authentication state during enable
- Better error messages for different failure scenarios
- Graceful handling of sync failures during enablement

### 4. Robust Authentication Verification

**New checks in calendar service**:
```javascript
if (!this.authService) {
    throw new Error('Authentication service not available');
}

if (!this.authService.isAuthenticated) {
    throw new Error('Must be authenticated to enable Google Calendar');
}

if (this.authService.isTokenExpired && this.authService.isTokenExpired()) {
    throw new Error('Authentication token has expired');
}
```

## Expected Behavior After Fix

### Successful Sign-In Flow:
1. ✅ User clicks "Sign in with Google"
2. ✅ Google OAuth popup appears (or silent auth if tokens cached)
3. ✅ Authentication completes successfully
4. ✅ Authentication state is refreshed and verified
5. ✅ Google Calendar integration is automatically enabled
6. ✅ UI updates to show "Connected" status
7. ✅ Success notification is displayed

### Error Scenarios:
- **Sign-in cancelled**: Clear error message, no calendar enablement attempted
- **Authentication fails**: Proper error handling, state cleanup
- **Calendar enablement fails**: Sign-in succeeds, but calendar shows warning

## Testing the Fix

### Manual Testing Steps:
1. Open CalenTask extension
2. Go to Settings > Google Calendar tab
3. Click "Sign in with Google"
4. Complete authentication process
5. Verify: No error messages appear
6. Verify: Status shows "Connected"
7. Verify: Google Calendar toggle is enabled
8. Verify: Success notification appears

### Console Logs to Look For:
```
Starting Google sign-in process...
Google sign-in successful, updating UI...
Refreshing authentication state...
Auth state after refresh: {isAuthenticated: true, ...}
Authentication verified, enabling Google Calendar...
Attempting to enable Google Calendar integration...
Auth service state: {exists: true, isAuthenticated: true, ...}
Google Calendar integration enabled successfully
```

### Using Test Page:
- Open `auth-test.html`
- Click "Sign In"
- Check log for detailed flow information
- Verify no errors occur during the process

## Files Modified

1. **`todo.js`**: Enhanced sign-in button event handler with proper sequencing
2. **`google-auth.js`**: Added `refreshAuthState()` method for state verification
3. **`google-calendar-service.js`**: Improved error handling and logging in `enable()` method
4. **`auth-test.html`**: Updated test page to verify the fix

## Benefits of the Fix

- ✅ **Eliminates sign-in errors**: Proper sequencing prevents authentication timing issues
- ✅ **Better user experience**: Clear success/error messages and notifications
- ✅ **Robust error handling**: Graceful handling of various failure scenarios
- ✅ **Enhanced debugging**: Comprehensive logging for troubleshooting
- ✅ **Reliable calendar integration**: Automatic enablement after successful authentication

The fix ensures that Google Calendar integration is only attempted after authentication is fully verified and propagated, eliminating the timing-related error that was occurring during the sign-in process.
